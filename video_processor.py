#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
视频处理工具模块
提供视频文件处理、格式转换和信息提取功能
"""

import cv2
import os
import numpy as np
from typing import Tuple, List, Optional, Dict, Any
from pathlib import Path
import logging


class VideoProcessor:
    """
    /*
     * @Name: VideoProcessor
     * @Description: 视频处理工具类，提供视频文件的各种处理功能
     *
     * @Input
     * 无特定输入参数
     *
     * @Output
     * 视频处理器实例
     *
     * @Edit History
     * Date: 2024-12-19
     * Time: 14:30:00
     * Author: JiaTao
     * Content: 创建视频处理工具类
     */
    """
    
    # 支持的视频格式
    SUPPORTED_FORMATS = ['.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv', '.m4v', '.3gp']
    
    def __init__(self):
        """
        /*
         * @Name: __init__
         * @Description: 初始化视频处理器
         *
         * @Input
         * None
         *
         * @Output
         * None
         *
         * @Edit History
         * Date: 2024-12-19
         * Time: 14:30:00
         * Author: Jia<PERSON>ao
         * Content: 初始化视频处理器
         */
        """
        self.logger = logging.getLogger(__name__)
    
    @staticmethod
    def validate_video_path(video_path: str) -> bool:
        """
        /*
         * @Name: validate_video_path
         * @Description: 验证视频文件路径是否有效
         *
         * @Input
         * video_path: str - 视频文件路径
         *
         * @Output
         * bool - 路径是否有效
         *
         * @Edit History
         * Date: 2024-12-19
         * Time: 14:30:00
         * Author: JiaTao
         * Content: 验证视频文件路径和格式
         */
        """
        if not os.path.exists(video_path):
            return False
        
        file_ext = Path(video_path).suffix.lower()
        return file_ext in VideoProcessor.SUPPORTED_FORMATS
    
    @staticmethod
    def get_video_info(video_path: str) -> Dict[str, Any]:
        """
        /*
         * @Name: get_video_info
         * @Description: 获取视频文件的详细信息
         *
         * @Input
         * video_path: str - 视频文件路径
         *
         * @Output
         * Dict[str, Any] - 视频信息字典
         *
         * @Edit History
         * Date: 2024-12-19
         * Time: 14:30:00
         * Author: JiaTao
         * Content: 提取视频文件的基本信息
         */
        """
        if not VideoProcessor.validate_video_path(video_path):
            raise ValueError(f"无效的视频文件: {video_path}")
        
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise ValueError(f"无法打开视频文件: {video_path}")
        
        try:
            info = {
                'filename': os.path.basename(video_path),
                'filepath': os.path.abspath(video_path),
                'filesize': os.path.getsize(video_path),
                'width': int(cap.get(cv2.CAP_PROP_FRAME_WIDTH)),
                'height': int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT)),
                'fps': cap.get(cv2.CAP_PROP_FPS),
                'frame_count': int(cap.get(cv2.CAP_PROP_FRAME_COUNT)),
                'duration': 0.0,
                'codec': '',
                'format': Path(video_path).suffix.lower()
            }
            
            # 计算视频时长
            if info['fps'] > 0:
                info['duration'] = info['frame_count'] / info['fps']
            
            # 尝试获取编码器信息
            fourcc = cap.get(cv2.CAP_PROP_FOURCC)
            if fourcc:
                info['codec'] = "".join([chr((int(fourcc) >> 8 * i) & 0xFF) for i in range(4)])
            
            return info
        
        finally:
            cap.release()
    
    @staticmethod
    def resize_video(
        input_path: str,
        output_path: str,
        target_size: Tuple[int, int],
        maintain_aspect_ratio: bool = True
    ) -> bool:
        """
        /*
         * @Name: resize_video
         * @Description: 调整视频分辨率
         *
         * @Input
         * input_path: str - 输入视频路径
         * output_path: str - 输出视频路径
         * target_size: Tuple[int, int] - 目标分辨率(宽度, 高度)
         * maintain_aspect_ratio: bool - 是否保持宽高比
         *
         * @Output
         * bool - 处理是否成功
         *
         * @Edit History
         * Date: 2024-12-19
         * Time: 14:30:00
         * Author: JiaTao
         * Content: 实现视频分辨率调整功能
         */
        """
        cap = cv2.VideoCapture(input_path)
        if not cap.isOpened():
            return False
        
        # 获取原始视频信息
        original_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        original_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        
        # 计算新的尺寸
        target_width, target_height = target_size
        
        if maintain_aspect_ratio:
            # 保持宽高比
            aspect_ratio = original_width / original_height
            if target_width / target_height > aspect_ratio:
                target_width = int(target_height * aspect_ratio)
            else:
                target_height = int(target_width / aspect_ratio)
        
        # 创建视频写入器
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, fps, (target_width, target_height))
        
        try:
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # 调整帧大小
                resized_frame = cv2.resize(frame, (target_width, target_height))
                out.write(resized_frame)
            
            return True
        
        except Exception as e:
            print(f"视频调整大小失败: {e}")
            return False
        
        finally:
            cap.release()
            out.release()
    
    @staticmethod
    def extract_frames(
        video_path: str,
        output_dir: str,
        frame_interval: int = 1,
        start_frame: int = 0,
        end_frame: Optional[int] = None
    ) -> List[str]:
        """
        /*
         * @Name: extract_frames
         * @Description: 从视频中提取帧图像
         *
         * @Input
         * video_path: str - 视频文件路径
         * output_dir: str - 输出目录
         * frame_interval: int - 帧间隔
         * start_frame: int - 起始帧
         * end_frame: Optional[int] - 结束帧
         *
         * @Output
         * List[str] - 提取的帧文件路径列表
         *
         * @Edit History
         * Date: 2024-12-19
         * Time: 14:30:00
         * Author: JiaTao
         * Content: 实现视频帧提取功能
         */
        """
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise ValueError(f"无法打开视频文件: {video_path}")
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        if end_frame is None:
            end_frame = total_frames
        
        extracted_files = []
        frame_count = 0
        
        try:
            while True:
                ret, frame = cap.read()
                if not ret or frame_count >= end_frame:
                    break
                
                if frame_count >= start_frame and frame_count % frame_interval == 0:
                    # 保存帧
                    filename = f"frame_{frame_count:06d}.jpg"
                    filepath = os.path.join(output_dir, filename)
                    cv2.imwrite(filepath, frame)
                    extracted_files.append(filepath)
                
                frame_count += 1
            
            return extracted_files
        
        finally:
            cap.release()
    
    @staticmethod
    def create_video_from_frames(
        frames_dir: str,
        output_path: str,
        fps: float = 30.0,
        frame_pattern: str = "frame_%06d.jpg"
    ) -> bool:
        """
        /*
         * @Name: create_video_from_frames
         * @Description: 从帧图像创建视频
         *
         * @Input
         * frames_dir: str - 帧图像目录
         * output_path: str - 输出视频路径
         * fps: float - 帧率
         * frame_pattern: str - 帧文件名模式
         *
         * @Output
         * bool - 创建是否成功
         *
         * @Edit History
         * Date: 2024-12-19
         * Time: 14:30:00
         * Author: JiaTao
         * Content: 实现从帧图像创建视频功能
         */
        """
        # 获取帧文件列表
        frame_files = []
        for filename in sorted(os.listdir(frames_dir)):
            if filename.endswith(('.jpg', '.jpeg', '.png')):
                frame_files.append(os.path.join(frames_dir, filename))
        
        if not frame_files:
            return False
        
        # 读取第一帧获取尺寸
        first_frame = cv2.imread(frame_files[0])
        if first_frame is None:
            return False
        
        height, width = first_frame.shape[:2]
        
        # 创建视频写入器
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
        
        try:
            for frame_file in frame_files:
                frame = cv2.imread(frame_file)
                if frame is not None:
                    out.write(frame)
            
            return True
        
        except Exception as e:
            print(f"创建视频失败: {e}")
            return False
        
        finally:
            out.release()
    
    @staticmethod
    def get_optimal_resolution(original_size: Tuple[int, int], max_size: int = 1280) -> Tuple[int, int]:
        """
        /*
         * @Name: get_optimal_resolution
         * @Description: 获取最优分辨率
         *
         * @Input
         * original_size: Tuple[int, int] - 原始分辨率
         * max_size: int - 最大尺寸限制
         *
         * @Output
         * Tuple[int, int] - 最优分辨率
         *
         * @Edit History
         * Date: 2024-12-19
         * Time: 14:30:00
         * Author: JiaTao
         * Content: 计算最优分辨率以平衡性能和质量
         */
        """
        width, height = original_size
        
        # 如果分辨率已经在合理范围内，直接返回
        if max(width, height) <= max_size:
            return width, height
        
        # 计算缩放比例
        scale = max_size / max(width, height)
        new_width = int(width * scale)
        new_height = int(height * scale)
        
        # 确保尺寸是偶数（某些编码器要求）
        new_width = new_width if new_width % 2 == 0 else new_width - 1
        new_height = new_height if new_height % 2 == 0 else new_height - 1
        
        return new_width, new_height
    
    @staticmethod
    def print_video_info(video_path: str) -> None:
        """
        /*
         * @Name: print_video_info
         * @Description: 打印视频信息
         *
         * @Input
         * video_path: str - 视频文件路径
         *
         * @Output
         * None
         *
         * @Edit History
         * Date: 2024-12-19
         * Time: 14:30:00
         * Author: JiaTao
         * Content: 格式化打印视频文件信息
         */
        """
        try:
            info = VideoProcessor.get_video_info(video_path)
            
            print(f"\n=== 视频信息 ===")
            print(f"文件名: {info['filename']}")
            print(f"文件路径: {info['filepath']}")
            print(f"文件大小: {info['filesize'] / (1024*1024):.2f} MB")
            print(f"分辨率: {info['width']} x {info['height']}")
            print(f"帧率: {info['fps']:.2f} FPS")
            print(f"总帧数: {info['frame_count']}")
            print(f"时长: {info['duration']:.2f} 秒")
            print(f"编码器: {info['codec']}")
            print(f"格式: {info['format']}")
            print("=" * 20)
        
        except Exception as e:
            print(f"获取视频信息失败: {e}")


if __name__ == "__main__":
    # 示例用法
    processor = VideoProcessor()
    
    # 测试视频信息获取
    test_video = "test_video.mp4"
    if os.path.exists(test_video):
        VideoProcessor.print_video_info(test_video)
