#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
目标跟踪系统测试脚本
用于验证系统各个组件的功能
"""

import os
import sys
import unittest
import tempfile
import numpy as np
import cv2
from unittest.mock import patch, MagicMock

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from object_tracker import ObjectTracker
from tracker_config import ConfigManager, TrackerConfig, create_default_configs
from video_processor import VideoProcessor


class TestTrackerConfig(unittest.TestCase):
    """
    /*
     * @Name: TestTrackerConfig
     * @Description: 测试配置管理功能
     *
     * @Input
     * unittest.TestCase基类
     *
     * @Output
     * 测试结果
     *
     * @Edit History
     * Date: 2024-12-19
     * Time: 14:30:00
     * Author: JiaTao
     * Content: 创建配置管理测试类
     */
    """
    
    def setUp(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        self.config_file = os.path.join(self.temp_dir, "test_config.yaml")
    
    def test_default_config(self):
        """测试默认配置"""
        config = TrackerConfig()
        self.assertEqual(config.tracker_type, "botsort")
        self.assertEqual(config.track_high_thresh, 0.25)
        self.assertEqual(config.conf_threshold, 0.25)
    
    def test_config_manager_creation(self):
        """测试配置管理器创建"""
        manager = ConfigManager()
        self.assertIsInstance(manager.config, TrackerConfig)
    
    def test_config_update(self):
        """测试配置更新"""
        manager = ConfigManager()
        manager.update_config(track_high_thresh=0.3, conf_threshold=0.4)
        
        self.assertEqual(manager.config.track_high_thresh, 0.3)
        self.assertEqual(manager.config.conf_threshold, 0.4)
    
    def test_config_save_load(self):
        """测试配置保存和加载"""
        manager = ConfigManager()
        manager.update_config(track_high_thresh=0.35)
        manager.save_config(self.config_file)
        
        # 加载配置
        new_manager = ConfigManager(self.config_file)
        self.assertEqual(new_manager.config.track_high_thresh, 0.35)


class TestVideoProcessor(unittest.TestCase):
    """
    /*
     * @Name: TestVideoProcessor
     * @Description: 测试视频处理功能
     *
     * @Input
     * unittest.TestCase基类
     *
     * @Output
     * 测试结果
     *
     * @Edit History
     * Date: 2024-12-19
     * Time: 14:30:00
     * Author: JiaTao
     * Content: 创建视频处理测试类
     */
    """
    
    def setUp(self):
        """设置测试环境"""
        self.processor = VideoProcessor()
        self.temp_dir = tempfile.mkdtemp()
    
    def test_validate_video_path(self):
        """测试视频路径验证"""
        # 测试不存在的文件
        self.assertFalse(VideoProcessor.validate_video_path("nonexistent.mp4"))
        
        # 测试不支持的格式
        temp_file = os.path.join(self.temp_dir, "test.txt")
        with open(temp_file, 'w') as f:
            f.write("test")
        self.assertFalse(VideoProcessor.validate_video_path(temp_file))
    
    def test_get_optimal_resolution(self):
        """测试最优分辨率计算"""
        # 测试不需要缩放的情况
        result = VideoProcessor.get_optimal_resolution((640, 480), 1280)
        self.assertEqual(result, (640, 480))
        
        # 测试需要缩放的情况
        result = VideoProcessor.get_optimal_resolution((1920, 1080), 1280)
        self.assertEqual(result, (1280, 720))
    
    def create_test_video(self, filename, width=640, height=480, fps=30, duration=1):
        """创建测试视频文件"""
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(filename, fourcc, fps, (width, height))
        
        frames = int(fps * duration)
        for i in range(frames):
            # 创建彩色帧
            frame = np.random.randint(0, 255, (height, width, 3), dtype=np.uint8)
            out.write(frame)
        
        out.release()
        return filename


class TestObjectTracker(unittest.TestCase):
    """
    /*
     * @Name: TestObjectTracker
     * @Description: 测试目标跟踪器功能
     *
     * @Input
     * unittest.TestCase基类
     *
     * @Output
     * 测试结果
     *
     * @Edit History
     * Date: 2024-12-19
     * Time: 14:30:00
     * Author: JiaTao
     * Content: 创建目标跟踪器测试类
     */
    """
    
    def setUp(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        self.model_path = "yolo11m.pt"  # 假设模型文件存在
    
    @patch('object_tracker.YOLO')
    def test_tracker_initialization(self, mock_yolo):
        """测试跟踪器初始化"""
        # 模拟YOLO模型
        mock_model = MagicMock()
        mock_yolo.return_value = mock_model
        
        # 模拟模型文件存在
        with patch('os.path.exists', return_value=True):
            tracker = ObjectTracker(
                model_path=self.model_path,
                conf_threshold=0.3,
                iou_threshold=0.5
            )
            
            self.assertEqual(tracker.conf_threshold, 0.3)
            self.assertEqual(tracker.iou_threshold, 0.5)
            self.assertIsNotNone(tracker.model)
    
    def test_tracker_validation(self):
        """测试跟踪器参数验证"""
        # 测试不存在的模型文件
        with self.assertRaises(FileNotFoundError):
            ObjectTracker(model_path="nonexistent_model.pt")
    
    @patch('object_tracker.YOLO')
    @patch('cv2.VideoCapture')
    def test_track_video_mock(self, mock_cap, mock_yolo):
        """测试视频跟踪功能（模拟）"""
        # 模拟YOLO模型
        mock_model = MagicMock()
        mock_yolo.return_value = mock_model
        
        # 模拟视频捕获
        mock_video = MagicMock()
        mock_cap.return_value = mock_video
        mock_video.isOpened.return_value = True
        mock_video.get.side_effect = lambda prop: {
            cv2.CAP_PROP_FPS: 30,
            cv2.CAP_PROP_FRAME_WIDTH: 640,
            cv2.CAP_PROP_FRAME_HEIGHT: 480,
            cv2.CAP_PROP_FRAME_COUNT: 30
        }.get(prop, 0)
        
        # 模拟读取帧
        mock_video.read.side_effect = [
            (True, np.zeros((480, 640, 3), dtype=np.uint8)),
            (False, None)  # 结束
        ]
        
        # 模拟跟踪结果
        mock_result = MagicMock()
        mock_result.boxes = None
        mock_model.track.return_value = [mock_result]
        
        # 模拟模型文件存在
        with patch('os.path.exists', return_value=True):
            tracker = ObjectTracker(model_path=self.model_path)
            
            # 执行跟踪
            stats = tracker.track_video(
                video_path="test_video.mp4",
                show_display=False
            )
            
            self.assertIsInstance(stats, dict)
            self.assertIn('total_frames', stats)


class TestIntegration(unittest.TestCase):
    """
    /*
     * @Name: TestIntegration
     * @Description: 集成测试
     *
     * @Input
     * unittest.TestCase基类
     *
     * @Output
     * 测试结果
     *
     * @Edit History
     * Date: 2024-12-19
     * Time: 14:30:00
     * Author: JiaTao
     * Content: 创建集成测试类
     */
    """
    
    def test_create_default_configs(self):
        """测试创建默认配置文件"""
        with patch('os.makedirs'):
            with patch('builtins.open', create=True):
                with patch('yaml.dump'):
                    try:
                        create_default_configs()
                        # 如果没有异常，测试通过
                        self.assertTrue(True)
                    except Exception as e:
                        self.fail(f"创建默认配置失败: {e}")


def run_basic_tests():
    """
    /*
     * @Name: run_basic_tests
     * @Description: 运行基础功能测试
     *
     * @Input
     * None
     *
     * @Output
     * None
     *
     * @Edit History
     * Date: 2024-12-19
     * Time: 14:30:00
     * Author: JiaTao
     * Content: 运行基础功能测试
     */
    """
    print("=== 目标跟踪系统基础测试 ===")
    
    # 测试配置管理
    print("1. 测试配置管理...")
    try:
        config = ConfigManager()
        config.update_config(track_high_thresh=0.3)
        print("   ✓ 配置管理功能正常")
    except Exception as e:
        print(f"   ✗ 配置管理测试失败: {e}")
    
    # 测试视频处理
    print("2. 测试视频处理...")
    try:
        processor = VideoProcessor()
        result = processor.get_optimal_resolution((1920, 1080), 1280)
        assert result == (1280, 720)
        print("   ✓ 视频处理功能正常")
    except Exception as e:
        print(f"   ✗ 视频处理测试失败: {e}")
    
    # 测试模型文件检查
    print("3. 测试模型文件...")
    if os.path.exists("yolo11m.pt"):
        print("   ✓ 模型文件存在")
    else:
        print("   ⚠ 模型文件不存在，首次运行时会自动下载")
    
    print("=== 基础测试完成 ===")


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="目标跟踪系统测试")
    parser.add_argument("--unit", action="store_true", help="运行单元测试")
    parser.add_argument("--basic", action="store_true", help="运行基础功能测试")
    
    args = parser.parse_args()
    
    if args.unit:
        # 运行单元测试
        unittest.main(argv=[''], exit=False, verbosity=2)
    elif args.basic:
        # 运行基础测试
        run_basic_tests()
    else:
        # 默认运行基础测试
        run_basic_tests()
        print("\n使用 --unit 参数运行完整单元测试")
        print("使用 --basic 参数运行基础功能测试")
