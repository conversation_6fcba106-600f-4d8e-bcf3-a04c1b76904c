#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
桌面端测试脚本
专门用于桌面环境的目标跟踪测试
"""

import cv2
import os
import sys
import time
import argparse
from pathlib import Path

from object_tracker import ObjectTracker
from video_processor import VideoProcessor


def test_opencv_display():
    """
    /*
     * @Name: test_opencv_display
     * @Description: 测试OpenCV显示功能
     *
     * @Input
     * None
     *
     * @Output
     * bool - 显示功能是否正常
     *
     * @Edit History
     * Date: 2024-12-19
     * Time: 16:15:00
     * Author: JiaTao
     * Content: 测试OpenCV显示功能
     */
    """
    print("测试OpenCV显示功能...")
    
    try:
        # 创建测试图像
        import numpy as np
        test_image = np.zeros((480, 640, 3), dtype=np.uint8)
        test_image[:] = (50, 100, 150)  # 蓝色背景
        
        # 添加文字
        cv2.putText(test_image, "OpenCV Display Test", (50, 240), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        cv2.putText(test_image, "Press 'q' to continue", (50, 280), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        # 显示测试窗口
        cv2.namedWindow('OpenCV测试', cv2.WINDOW_NORMAL)
        cv2.resizeWindow('OpenCV测试', 640, 480)
        cv2.imshow('OpenCV测试', test_image)
        
        print("如果您看到了测试窗口，请按 'q' 键继续...")
        
        # 等待按键
        while True:
            key = cv2.waitKey(30) & 0xFF
            if key == ord('q') or key == 27:
                break
        
        cv2.destroyAllWindows()
        print("✓ OpenCV显示功能正常")
        return True
        
    except Exception as e:
        print(f"✗ OpenCV显示功能异常: {e}")
        return False


def test_video_tracking_desktop(video_path: str, resize: bool = True):
    """
    /*
     * @Name: test_video_tracking_desktop
     * @Description: 桌面端视频跟踪测试
     *
     * @Input
     * video_path: str - 视频文件路径
     * resize: bool - 是否resize输出
     *
     * @Output
     * None
     *
     * @Edit History
     * Date: 2024-12-19
     * Time: 16:15:00
     * Author: JiaTao
     * Content: 桌面端视频跟踪测试
     */
    """
    print(f"\n=== 桌面端视频跟踪测试 ===")
    print(f"视频文件: {video_path}")
    print(f"Resize输出: {'是' if resize else '否'}")
    
    try:
        # 显示视频信息
        VideoProcessor.print_video_info(video_path)
        
        # 创建跟踪器
        tracker = ObjectTracker(
            model_path="yolo11m.pt",
            tracker_config="botsort.yaml",
            conf_threshold=0.3,
            iou_threshold=0.5
        )
        
        print("\n开始跟踪...")
        print("控制说明:")
        print("- 按 'q' 键或 ESC 键退出")
        print("- 窗口可以调整大小")
        print("- 如果窗口显示异常，程序会自动继续处理")
        
        # 执行跟踪
        output_path = "outputs/desktop_test_output.mp4"
        stats = tracker.track_video(
            video_path=video_path,
            output_path=output_path,
            show_display=True,
            resize_output=resize
        )
        
        print("\n跟踪完成！")
        print("统计信息:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        if os.path.exists(output_path):
            print(f"\n输出视频已保存: {output_path}")
            
            # 询问是否播放输出视频
            response = input("\n是否播放输出视频? (y/n): ").strip().lower()
            if response == 'y':
                play_video(output_path)
        
    except Exception as e:
        print(f"跟踪测试失败: {e}")


def play_video(video_path: str):
    """
    /*
     * @Name: play_video
     * @Description: 播放视频文件
     *
     * @Input
     * video_path: str - 视频文件路径
     *
     * @Output
     * None
     *
     * @Edit History
     * Date: 2024-12-19
     * Time: 16:15:00
     * Author: JiaTao
     * Content: 播放视频文件
     */
    """
    print(f"播放视频: {video_path}")
    print("按 'q' 键或 ESC 键退出播放")
    
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print("无法打开视频文件")
        return
    
    # 获取视频属性
    fps = cap.get(cv2.CAP_PROP_FPS)
    delay = int(1000 / fps) if fps > 0 else 30
    
    # 创建窗口（只创建一次）
    window_name = '视频播放'
    cv2.namedWindow(window_name, cv2.WINDOW_NORMAL)
    cv2.resizeWindow(window_name, 1280, 720)

    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                print("视频播放完成")
                break

            cv2.imshow(window_name, frame)

            key = cv2.waitKey(delay) & 0xFF
            if key == ord('q') or key == 27:
                break
    
    except Exception as e:
        print(f"播放出错: {e}")
    
    finally:
        cap.release()
        cv2.destroyAllWindows()


def test_camera_tracking():
    """
    /*
     * @Name: test_camera_tracking
     * @Description: 测试摄像头跟踪
     *
     * @Input
     * None
     *
     * @Output
     * None
     *
     * @Edit History
     * Date: 2024-12-19
     * Time: 16:15:00
     * Author: JiaTao
     * Content: 测试摄像头跟踪功能
     */
    """
    print("\n=== 摄像头跟踪测试 ===")
    
    # 检查摄像头
    cap = cv2.VideoCapture(0)
    if not cap.isOpened():
        print("无法打开摄像头")
        return
    cap.release()
    
    try:
        tracker = ObjectTracker(
            model_path="yolo11m.pt",
            tracker_config="botsort.yaml",
            conf_threshold=0.3,
            iou_threshold=0.5
        )
        
        print("开始摄像头跟踪...")
        print("按 'q' 键或 ESC 键退出")
        
        tracker.track_realtime(
            camera_index=0,
            output_path="outputs/camera_test_output.mp4",
            resize_output=True
        )
        
    except Exception as e:
        print(f"摄像头跟踪失败: {e}")


def main():
    """
    /*
     * @Name: main
     * @Description: 主函数
     *
     * @Input
     * None
     *
     * @Output
     * None
     *
     * @Edit History
     * Date: 2024-12-19
     * Time: 16:15:00
     * Author: JiaTao
     * Content: 桌面端测试主函数
     */
    """
    parser = argparse.ArgumentParser(description="桌面端目标跟踪测试")
    parser.add_argument("--video", type=str, help="测试视频文件路径")
    parser.add_argument("--camera", action="store_true", help="测试摄像头跟踪")
    parser.add_argument("--no-resize", action="store_true", help="不使用resize功能")
    parser.add_argument("--play", type=str, help="播放指定视频文件")
    
    args = parser.parse_args()
    
    print("=== 桌面端目标跟踪系统测试 ===")
    
    # 首先测试OpenCV显示功能
    if not test_opencv_display():
        print("OpenCV显示功能异常，请检查图形界面环境")
        return
    
    # 创建输出目录
    os.makedirs("outputs", exist_ok=True)
    
    if args.play:
        # 播放视频
        play_video(args.play)
    elif args.camera:
        # 测试摄像头跟踪
        test_camera_tracking()
    elif args.video:
        # 测试视频跟踪
        test_video_tracking_desktop(args.video, not args.no_resize)
    else:
        # 交互式选择
        print("\n请选择测试项目:")
        print("1. 视频文件跟踪测试")
        print("2. 摄像头跟踪测试")
        print("3. 播放视频文件")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-3): ").strip()
        
        if choice == "1":
            # 查找测试视频
            video_files = []
            for ext in ['.mp4', '.avi', '.mov', '.mkv']:
                video_files.extend(Path('.').rglob(f'*{ext}'))
            
            if video_files:
                print("\n找到的视频文件:")
                for i, video in enumerate(video_files[:5]):  # 只显示前5个
                    print(f"{i+1}. {video}")
                
                try:
                    idx = int(input("请选择视频文件 (输入序号): ")) - 1
                    if 0 <= idx < len(video_files):
                        test_video_tracking_desktop(str(video_files[idx]))
                    else:
                        print("无效选择")
                except ValueError:
                    print("无效输入")
            else:
                print("未找到视频文件")
        
        elif choice == "2":
            test_camera_tracking()
        
        elif choice == "3":
            video_path = input("请输入视频文件路径: ").strip()
            if os.path.exists(video_path):
                play_video(video_path)
            else:
                print("视频文件不存在")
        
        elif choice == "0":
            print("退出测试")
        
        else:
            print("无效选择")


if __name__ == "__main__":
    main()
