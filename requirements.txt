# YOLOv11目标跟踪系统依赖项
# 基于Ultralytics框架和BotSORT跟踪算法

# 核心依赖
ultralytics>=8.0.0
torch>=1.8.0
torchvision>=0.9.0
opencv-python>=4.6.0
numpy>=1.23.0

# 图像处理
Pillow>=7.1.2
matplotlib>=3.3.0

# 数据处理
pandas>=1.1.4
scipy>=1.4.1

# 配置管理
PyYAML>=5.3.1

# 系统工具
psutil
py-cpuinfo
tqdm>=4.64.0
requests>=2.23.0

# 跟踪算法依赖
lap>=0.5.12  # 用于BotSORT和ByteTrack跟踪器

# 可选依赖 (用于高级功能)
# 导出功能
# onnx>=1.12.0
# openvino>=2024.0.0
# tensorrt  # 需要NVIDIA GPU

# 可视化增强
# seaborn
# plotly

# 开发工具
# pytest
# black
# flake8
