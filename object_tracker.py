#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
目标跟踪系统核心模块
基于YOLOv11和BotSORT算法实现多目标跟踪
"""

import os
import cv2
import numpy as np
from typing import Optional, Tuple, List, Dict, Any
from pathlib import Path
import logging
from collections import defaultdict

from ultralytics import YOLO
from ultralytics.utils.plotting import Annotator, colors
from video_processor import VideoProcessor


class ObjectTracker:
    """
    /*
     * @Name: ObjectTracker
     * @Description: 基于YOLOv11和BotSORT算法的目标跟踪系统
     *
     * @Input
     * model_path: str - YOLO模型权重文件路径
     * tracker_config: str - 跟踪器配置文件路径
     * conf_threshold: float - 置信度阈值
     * iou_threshold: float - IoU阈值
     * device: str - 推理设备
     *
     * @Output
     * 初始化的目标跟踪器实例
     *
     * @Edit History
     * Date: 2024-12-19
     * Time: 14:30:00
     * Author: JiaTao
     * Content: 创建ObjectTracker类，实现基于YOLOv11和BotSORT的目标跟踪功能
     */
    """
    
    def __init__(
        self,
        model_path: str = "yolo11m.pt",
        tracker_config: str = "botsort.yaml",
        conf_threshold: float = 0.25,
        iou_threshold: float = 0.45,
        device: str = "auto"
    ):
        """
        /*
         * @Name: __init__
         * @Description: 初始化目标跟踪器
         *
         * @Input
         * model_path: str - YOLO模型权重文件路径
         * tracker_config: str - 跟踪器配置文件路径
         * conf_threshold: float - 置信度阈值
         * iou_threshold: float - IoU阈值
         * device: str - 推理设备
         *
         * @Output
         * None
         *
         * @Edit History
         * Date: 2024-12-19
         * Time: 14:30:00
         * Author: JiaTao
         * Content: 初始化跟踪器参数和模型
         */
        """
        self.model_path = model_path
        self.tracker_config = tracker_config
        self.conf_threshold = conf_threshold
        self.iou_threshold = iou_threshold
        self.device = device
        
        # 初始化日志
        self._setup_logging()
        
        # 验证模型文件
        self._validate_model_path()
        
        # 加载YOLO模型
        self.model = self._load_model()
        
        # 跟踪历史记录
        self.track_history = defaultdict(lambda: [])
        
        # 统计信息
        self.stats = {
            "total_frames": 0,
            "tracked_objects": 0,
            "processing_time": 0.0
        }
        
        self.logger.info(f"目标跟踪器初始化完成 - 模型: {model_path}, 跟踪器: {tracker_config}")
    
    def _setup_logging(self) -> None:
        """
        /*
         * @Name: _setup_logging
         * @Description: 设置日志记录器
         *
         * @Input
         * None
         *
         * @Output
         * None
         *
         * @Edit History
         * Date: 2024-12-19
         * Time: 14:30:00
         * Author: JiaTao
         * Content: 配置日志记录器
         */
        """
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('tracker.log', encoding='utf-8')
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def _validate_model_path(self) -> None:
        """
        /*
         * @Name: _validate_model_path
         * @Description: 验证模型文件路径是否有效
         *
         * @Input
         * None
         *
         * @Output
         * None
         *
         * @Edit History
         * Date: 2024-12-19
         * Time: 14:30:00
         * Author: JiaTao
         * Content: 验证模型文件存在性
         */
        """
        if not os.path.exists(self.model_path):
            raise FileNotFoundError(f"模型文件不存在: {self.model_path}")
        
        if not self.model_path.endswith(('.pt', '.onnx', '.engine')):
            raise ValueError(f"不支持的模型格式: {self.model_path}")
    
    def _load_model(self) -> YOLO:
        """
        /*
         * @Name: _load_model
         * @Description: 加载YOLO模型
         *
         * @Input
         * None
         *
         * @Output
         * YOLO - 加载的YOLO模型实例
         *
         * @Edit History
         * Date: 2024-12-19
         * Time: 14:30:00
         * Author: JiaTao
         * Content: 加载并配置YOLO模型
         */
        """
        try:
            model = YOLO(self.model_path)
            self.logger.info(f"成功加载模型: {self.model_path}")
            return model
        except Exception as e:
            self.logger.error(f"模型加载失败: {e}")
            raise
    
    def track_video(
        self,
        video_path: str,
        output_path: Optional[str] = None,
        show_display: bool = True,
        save_frames: bool = False,
        resize_output: bool = False
    ) -> Dict[str, Any]:
        """
        /*
         * @Name: track_video
         * @Description: 对视频文件进行目标跟踪
         *
         * @Input
         * video_path: str - 输入视频文件路径
         * output_path: Optional[str] - 输出视频文件路径
         * show_display: bool - 是否显示实时跟踪结果
         * save_frames: bool - 是否保存跟踪帧
         * resize_output: bool - 是否将输出resize到960分辨率
         *
         * @Output
         * Dict[str, Any] - 跟踪结果统计信息
         *
         * @Edit History
         * Date: 2024-12-19
         * Time: 14:30:00
         * Author: JiaTao
         * Content: 实现视频目标跟踪主要逻辑，增加resize功能
         */
        """
        # 验证输入视频
        if not os.path.exists(video_path):
            raise FileNotFoundError(f"视频文件不存在: {video_path}")
        
        # 打开视频文件
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise ValueError(f"无法打开视频文件: {video_path}")
        
        # 获取视频属性
        fps = int(cap.get(cv2.CAP_PROP_FPS))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

        self.logger.info(f"视频信息 - 分辨率: {width}x{height}, 帧率: {fps}, 总帧数: {total_frames}")

        # 计算输出尺寸
        output_width, output_height = width, height
        if resize_output:
            output_width, output_height = VideoProcessor.get_optimal_resolution((width, height), 960)
            self.logger.info(f"输出将resize到: {output_width}x{output_height}")

        # 初始化视频写入器
        out = None
        if output_path:
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_path, fourcc, fps, (output_width, output_height))
            self.logger.info(f"输出视频将保存到: {output_path}")
        
        # 重置统计信息
        self.stats = {
            "total_frames": 0,
            "tracked_objects": 0,
            "processing_time": 0.0
        }
        
        frame_count = 0
        window_created = False

        try:
            while True:
                ret, frame = cap.read()
                if not ret:
                    self.logger.info("视频处理完成")
                    break
                
                frame_count += 1
                
                # 执行跟踪
                results = self.model.track(
                    frame,
                    conf=self.conf_threshold,
                    iou=self.iou_threshold,
                    tracker=self.tracker_config,
                    persist=True,
                    verbose=False
                )
                
                # 处理跟踪结果
                annotated_frame = self._process_tracking_results(frame, results[0])

                # 准备输出帧
                output_frame = annotated_frame
                display_frame = annotated_frame

                # 如果需要resize，对输出和显示帧进行resize
                if resize_output:
                    output_frame = cv2.resize(annotated_frame, (output_width, output_height))
                    # 显示时也使用resize后的帧，保持一致性
                    display_frame = output_frame

                # 保存视频帧
                if out is not None:
                    out.write(output_frame)

                # 显示实时结果
                if show_display:
                    try:
                        # 只在第一次创建窗口
                        if not window_created:
                            cv2.namedWindow('目标跟踪', cv2.WINDOW_NORMAL)
                            cv2.resizeWindow('目标跟踪', 1280, 720)
                            window_created = True
                            self.logger.info("显示窗口已创建，按 'q' 或 ESC 键退出")

                        cv2.imshow('目标跟踪', display_frame)

                        # 检查按键
                        key = cv2.waitKey(1) & 0xFF
                        if key == ord('q') or key == 27:  # 'q' 或 ESC 键
                            self.logger.info("用户中断跟踪")
                            break
                    except Exception as e:
                        self.logger.warning(f"显示窗口出错: {e}")
                        # 如果显示出错，继续处理但不显示
                        show_display = False
                
                # 更新统计信息
                self.stats["total_frames"] = frame_count
                
                # 显示进度
                if frame_count % 30 == 0:
                    progress = (frame_count / total_frames) * 100
                    self.logger.info(f"处理进度: {progress:.1f}% ({frame_count}/{total_frames})")
        
        except Exception as e:
            self.logger.error(f"视频处理过程中发生错误: {e}")
            raise
        
        finally:
            # 释放资源
            cap.release()
            if out is not None:
                out.release()
            if show_display:
                cv2.destroyAllWindows()
        
        self.logger.info(f"跟踪完成 - 处理帧数: {self.stats['total_frames']}, 跟踪对象: {self.stats['tracked_objects']}")
        return self.stats

    def _process_tracking_results(self, frame: np.ndarray, results: Any) -> np.ndarray:
        """
        /*
         * @Name: _process_tracking_results
         * @Description: 处理跟踪结果并在帧上绘制标注
         *
         * @Input
         * frame: np.ndarray - 原始视频帧
         * results: Any - YOLO跟踪结果
         *
         * @Output
         * np.ndarray - 标注后的视频帧
         *
         * @Edit History
         * Date: 2024-12-19
         * Time: 14:30:00
         * Author: JiaTao
         * Content: 处理跟踪结果并绘制边界框和ID
         */
        """
        annotated_frame = frame.copy()

        # 检查是否有跟踪结果
        if results.boxes is not None and results.boxes.id is not None:
            # 获取边界框、置信度、类别和跟踪ID
            boxes = results.boxes.xywh.cpu().numpy()
            confidences = results.boxes.conf.cpu().numpy()
            class_ids = results.boxes.cls.cpu().numpy().astype(int)
            track_ids = results.boxes.id.cpu().numpy().astype(int)

            # 创建标注器
            annotator = Annotator(annotated_frame, line_width=2, font_size=1)

            # 遍历每个检测结果
            for box, conf, cls_id, track_id in zip(boxes, confidences, class_ids, track_ids):
                # 转换边界框格式 (中心点x, 中心点y, 宽度, 高度) -> (x1, y1, x2, y2)
                x_center, y_center, width, height = box
                x1 = int(x_center - width / 2)
                y1 = int(y_center - height / 2)
                x2 = int(x_center + width / 2)
                y2 = int(y_center + height / 2)

                # 获取类别名称
                class_name = self.model.names[cls_id] if cls_id < len(self.model.names) else f"class_{cls_id}"

                # 创建标签
                label = f"ID:{track_id} {class_name} {conf:.2f}"

                # 获取颜色
                color = colors(track_id, True)

                # 绘制边界框和标签
                annotator.box_label([x1, y1, x2, y2], label, color=color)

                # 更新跟踪历史
                center_point = (int(x_center), int(y_center))
                self.track_history[track_id].append(center_point)

                # 限制历史轨迹长度
                if len(self.track_history[track_id]) > 30:
                    self.track_history[track_id].pop(0)

                # 绘制轨迹
                self._draw_trajectory(annotated_frame, track_id, color)

            # 更新统计信息
            self.stats["tracked_objects"] = len(track_ids)

        return annotated_frame

    def _draw_trajectory(self, frame: np.ndarray, track_id: int, color: Tuple[int, int, int]) -> None:
        """
        /*
         * @Name: _draw_trajectory
         * @Description: 在帧上绘制目标的运动轨迹
         *
         * @Input
         * frame: np.ndarray - 视频帧
         * track_id: int - 跟踪ID
         * color: Tuple[int, int, int] - 轨迹颜色
         *
         * @Output
         * None
         *
         * @Edit History
         * Date: 2024-12-19
         * Time: 14:30:00
         * Author: JiaTao
         * Content: 绘制目标运动轨迹
         */
        """
        if track_id in self.track_history and len(self.track_history[track_id]) > 1:
            points = self.track_history[track_id]

            # 绘制轨迹线
            for i in range(1, len(points)):
                thickness = max(1, int(3 * (i / len(points))))
                cv2.line(frame, points[i-1], points[i], color, thickness)

    def track_realtime(self, camera_index: int = 0, output_path: Optional[str] = None, resize_output: bool = False) -> None:
        """
        /*
         * @Name: track_realtime
         * @Description: 实时摄像头目标跟踪
         *
         * @Input
         * camera_index: int - 摄像头索引
         * output_path: Optional[str] - 输出视频文件路径
         * resize_output: bool - 是否将输出resize到960分辨率
         *
         * @Output
         * None
         *
         * @Edit History
         * Date: 2024-12-19
         * Time: 14:30:00
         * Author: JiaTao
         * Content: 实现实时摄像头跟踪功能，增加resize功能
         */
        """
        cap = cv2.VideoCapture(camera_index)
        if not cap.isOpened():
            raise ValueError(f"无法打开摄像头: {camera_index}")

        # 设置摄像头分辨率
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)

        fps = int(cap.get(cv2.CAP_PROP_FPS)) or 30
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

        # 计算输出尺寸
        output_width, output_height = width, height
        if resize_output:
            output_width, output_height = VideoProcessor.get_optimal_resolution((width, height), 960)
            self.logger.info(f"摄像头输出将resize到: {output_width}x{output_height}")

        # 初始化视频写入器
        out = None
        if output_path:
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_path, fourcc, fps, (output_width, output_height))

        self.logger.info("开始实时跟踪，按 'q' 键退出")
        window_created = False

        try:
            while True:
                ret, frame = cap.read()
                if not ret:
                    break

                # 执行跟踪
                results = self.model.track(
                    frame,
                    conf=self.conf_threshold,
                    iou=self.iou_threshold,
                    tracker=self.tracker_config,
                    persist=True,
                    verbose=False
                )

                # 处理跟踪结果
                annotated_frame = self._process_tracking_results(frame, results[0])

                # 准备输出帧
                output_frame = annotated_frame
                display_frame = annotated_frame

                # 如果需要resize，对输出和显示帧进行resize
                if resize_output:
                    output_frame = cv2.resize(annotated_frame, (output_width, output_height))
                    # 显示时也使用resize后的帧，保持一致性
                    display_frame = output_frame

                # 保存视频帧
                if out is not None:
                    out.write(output_frame)

                # 显示结果
                try:
                    # 只在第一次创建窗口
                    if not window_created:
                        cv2.namedWindow('实时目标跟踪', cv2.WINDOW_NORMAL)
                        cv2.resizeWindow('实时目标跟踪', 1280, 720)
                        window_created = True
                        self.logger.info("实时跟踪窗口已创建，按 'q' 或 ESC 键退出")

                    cv2.imshow('实时目标跟踪', display_frame)

                    # 检查退出条件
                    key = cv2.waitKey(1) & 0xFF
                    if key == ord('q') or key == 27:  # 'q' 或 ESC 键
                        break
                except Exception as e:
                    self.logger.warning(f"显示窗口出错: {e}")
                    break

        except Exception as e:
            self.logger.error(f"实时跟踪过程中发生错误: {e}")
            raise

        finally:
            cap.release()
            if out is not None:
                out.release()
            cv2.destroyAllWindows()
            self.logger.info("实时跟踪结束")

    def get_stats(self) -> Dict[str, Any]:
        """
        /*
         * @Name: get_stats
         * @Description: 获取跟踪统计信息
         *
         * @Input
         * None
         *
         * @Output
         * Dict[str, Any] - 统计信息字典
         *
         * @Edit History
         * Date: 2024-12-19
         * Time: 14:30:00
         * Author: JiaTao
         * Content: 返回跟踪统计信息
         */
        """
        return self.stats.copy()

    def reset_tracker(self) -> None:
        """
        /*
         * @Name: reset_tracker
         * @Description: 重置跟踪器状态
         *
         * @Input
         * None
         *
         * @Output
         * None
         *
         * @Edit History
         * Date: 2024-12-19
         * Time: 14:30:00
         * Author: JiaTao
         * Content: 重置跟踪历史和统计信息
         */
        """
        self.track_history.clear()
        self.stats = {
            "total_frames": 0,
            "tracked_objects": 0,
            "processing_time": 0.0
        }
        self.logger.info("跟踪器状态已重置")
