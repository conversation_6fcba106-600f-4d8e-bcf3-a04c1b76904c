# YOLOv11 + BotSORT 目标跟踪系统

基于YOLOv11模型和BotSORT跟踪算法的完整目标跟踪解决方案。

## 功能特性

- ✅ **YOLOv11检测**: 使用最新的YOLOv11模型进行高精度目标检测
- ✅ **BotSORT跟踪**: 集成先进的BotSORT多目标跟踪算法
- ✅ **实时处理**: 支持实时摄像头和视频文件跟踪
- ✅ **轨迹可视化**: 显示目标运动轨迹和跟踪ID
- ✅ **多格式支持**: 支持多种视频格式输入输出
- ✅ **配置管理**: 灵活的配置文件管理系统
- ✅ **命令行接口**: 完整的CLI工具
- ✅ **错误处理**: 完善的异常处理机制

## 系统要求

- Python 3.8+
- CUDA 11.0+ (可选，用于GPU加速)
- 8GB+ RAM (推荐)
- 支持的操作系统: Windows, Linux, macOS

## 安装说明

### 1. 安装依赖
```bash
# 安装项目依赖
pip install -r requirements.txt

# 验证安装
python main.py --help
```

### 2. 下载模型权重
确保工作目录中有 `yolo11m.pt` 权重文件，如果没有，程序会自动下载。

## 快速开始

### 1. 视频文件跟踪
```bash
# 基本用法
python main.py --source video.mp4 --output tracked_video.mp4 --show

# 使用自定义参数
python main.py --source video.mp4 --output result.mp4 --conf 0.3 --iou 0.5
```

### 2. 实时摄像头跟踪
```bash
# 默认摄像头
python main.py --source 0 --realtime --show

# 指定摄像头并保存
python main.py --source 1 --realtime --output camera_tracking.mp4
```

### 3. 查看视频信息
```bash
python main.py --info video.mp4
```

## 详细使用说明

### 命令行参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `--source` | str | - | 输入源：视频文件路径或摄像头索引 |
| `--output` | str | - | 输出视频文件路径 |
| `--model` | str | yolo11m.pt | YOLO模型权重文件 |
| `--tracker` | str | botsort.yaml | 跟踪器配置文件 |
| `--conf` | float | 0.25 | 置信度阈值 |
| `--iou` | float | 0.45 | IoU阈值 |
| `--device` | str | auto | 推理设备 (cpu/cuda/auto) |
| `--show` | bool | False | 显示实时跟踪结果 |
| `--save-frames` | bool | False | 保存跟踪帧图像 |
| `--realtime` | bool | False | 实时摄像头模式 |
| `--config` | str | - | 自定义配置文件路径 |

### Python API 使用

```python
from object_tracker import ObjectTracker
from tracker_config import ConfigManager

# 创建跟踪器
tracker = ObjectTracker(
    model_path="yolo11m.pt",
    tracker_config="botsort.yaml",
    conf_threshold=0.25,
    iou_threshold=0.45
)

# 跟踪视频
stats = tracker.track_video(
    video_path="input.mp4",
    output_path="output.mp4",
    show_display=True
)

# 实时跟踪
tracker.track_realtime(camera_index=0)
```

### 配置文件管理

```python
from tracker_config import ConfigManager, create_default_configs

# 创建默认配置
create_default_configs()

# 加载配置
config = ConfigManager("configs/botsort_custom.yaml")

# 更新配置
config.update_config(
    track_high_thresh=0.3,
    track_low_thresh=0.1,
    with_reid=True
)

# 保存配置
config.save_config("my_config.yaml")
```

## 配置参数说明

### BotSORT跟踪器参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `track_high_thresh` | 0.25 | 第一次关联阈值 |
| `track_low_thresh` | 0.1 | 第二次关联阈值 |
| `new_track_thresh` | 0.25 | 新轨迹初始化阈值 |
| `track_buffer` | 30 | 轨迹缓冲帧数 |
| `match_thresh` | 0.8 | 匹配阈值 |
| `proximity_thresh` | 0.5 | 空间邻近阈值 |
| `appearance_thresh` | 0.8 | 外观相似度阈值 |
| `with_reid` | False | 是否启用ReID |

## 项目结构

```
tracker/
├── object_tracker.py      # 核心跟踪器类
├── tracker_config.py      # 配置管理模块
├── video_processor.py     # 视频处理工具
├── main.py               # 主程序入口
├── requirements.txt      # 依赖项列表
├── TRACKER_README.md     # 使用说明
├── configs/             # 配置文件目录
├── logs/                # 日志文件目录
└── outputs/             # 输出文件目录
```

## 性能优化建议

1. **GPU加速**: 安装CUDA版本的PyTorch以获得更好性能
2. **模型选择**: 根据需求选择合适的YOLO模型
   - `yolo11n.pt`: 速度最快，精度较低
   - `yolo11s.pt`: 平衡速度和精度
   - `yolo11m.pt`: 推荐选择
   - `yolo11l.pt`: 高精度，速度较慢
   - `yolo11x.pt`: 最高精度，速度最慢

3. **分辨率调整**: 降低输入分辨率可提高处理速度
4. **参数调优**: 根据具体场景调整置信度和IoU阈值

## 常见问题

### Q: 如何提高跟踪精度？
A: 
- 调高置信度阈值 (`--conf`)
- 使用更大的YOLO模型
- 启用ReID功能
- 调整跟踪器参数

### Q: 如何提高处理速度？
A: 
- 使用GPU加速
- 选择较小的YOLO模型
- 降低输入分辨率
- 调低置信度阈值

### Q: 支持哪些视频格式？
A: 支持常见格式：MP4, AVI, MOV, MKV, FLV, WMV, M4V, 3GP

## 使用示例

### 示例1: 基本视频跟踪
```bash
python main.py --source demo.mp4 --output tracked_demo.mp4 --show
```

### 示例2: 高精度跟踪
```bash
python main.py --source video.mp4 --model yolo11x.pt --conf 0.3 --output high_quality.mp4
```

### 示例3: 实时摄像头跟踪
```bash
python main.py --source 0 --realtime --show
```

### 示例4: 创建自定义配置
```bash
python main.py --create-config
python main.py --source video.mp4 --config configs/botsort_custom.yaml
```

## 许可证

本项目基于AGPL-3.0许可证开源。

## 贡献

欢迎提交Issue和Pull Request来改进项目。

---

**注意**: 请确保在使用前已正确安装所有依赖项，并下载了相应的YOLO模型权重文件。
