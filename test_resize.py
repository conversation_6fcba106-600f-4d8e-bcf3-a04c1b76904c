#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试resize功能的简化脚本
"""

import os
import sys
from object_tracker import ObjectTracker


def test_resize_functionality():
    """
    /*
     * @Name: test_resize_functionality
     * @Description: 测试resize功能
     *
     * @Input
     * None
     *
     * @Output
     * None
     *
     * @Edit History
     * Date: 2024-12-19
     * Time: 16:30:00
     * Author: JiaTao
     * Content: 测试resize功能
     */
    """
    print("=== 测试Resize功能 ===")
    
    # 测试视频路径
    video_path = "inputs/20250604/43.0.229.214_20250604154632676001.mp4"
    
    if not os.path.exists(video_path):
        print(f"测试视频不存在: {video_path}")
        return
    
    try:
        # 创建跟踪器
        print("初始化跟踪器...")
        tracker = ObjectTracker(
            model_path="yolo11m.pt",
            tracker_config="botsort.yaml",
            conf_threshold=0.3,
            iou_threshold=0.5
        )
        
        print("开始处理视频...")
        print("原始分辨率: 3840x2160")
        print("输出分辨率: 960x540 (resize)")
        print("注意: 窗口只会创建一次，不会重复创建")
        print("按 'q' 或 ESC 键可以提前退出")
        
        # 执行跟踪（使用resize功能）
        stats = tracker.track_video(
            video_path=video_path,
            output_path="outputs/test_resize_output.mp4",
            show_display=True,
            resize_output=True
        )
        
        print("\n处理完成！")
        print("统计信息:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        output_file = "outputs/test_resize_output.mp4"
        if os.path.exists(output_file):
            print(f"\n输出文件已保存: {output_file}")
            
            # 获取文件大小
            file_size = os.path.getsize(output_file) / (1024 * 1024)  # MB
            print(f"文件大小: {file_size:.2f} MB")
        
    except KeyboardInterrupt:
        print("\n用户中断处理")
    except Exception as e:
        print(f"处理失败: {e}")


def compare_with_without_resize():
    """
    /*
     * @Name: compare_with_without_resize
     * @Description: 比较使用和不使用resize的效果
     *
     * @Input
     * None
     *
     * @Output
     * None
     *
     * @Edit History
     * Date: 2024-12-19
     * Time: 16:30:00
     * Author: JiaTao
     * Content: 比较resize效果
     */
    """
    print("=== 比较Resize效果 ===")
    
    video_path = "inputs/20250604/43.0.229.214_20250604154632676001.mp4"
    
    if not os.path.exists(video_path):
        print(f"测试视频不存在: {video_path}")
        return
    
    try:
        tracker = ObjectTracker(
            model_path="yolo11m.pt",
            tracker_config="botsort.yaml",
            conf_threshold=0.3,
            iou_threshold=0.5
        )
        
        print("1. 处理前30帧（不使用resize）...")
        # 只处理前30帧进行比较
        import cv2
        cap = cv2.VideoCapture(video_path)
        
        # 临时保存前30帧
        temp_video = "temp_30frames.mp4"
        fps = int(cap.get(cv2.CAP_PROP_FPS))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(temp_video, fourcc, fps, (width, height))
        
        for i in range(30):
            ret, frame = cap.read()
            if not ret:
                break
            out.write(frame)
        
        cap.release()
        out.release()
        
        # 测试不使用resize
        stats1 = tracker.track_video(
            video_path=temp_video,
            output_path="outputs/compare_no_resize.mp4",
            show_display=False,
            resize_output=False
        )
        
        print("2. 处理前30帧（使用resize）...")
        # 重置跟踪器
        tracker.reset_tracker()
        
        # 测试使用resize
        stats2 = tracker.track_video(
            video_path=temp_video,
            output_path="outputs/compare_with_resize.mp4",
            show_display=False,
            resize_output=True
        )
        
        print("\n比较结果:")
        print("不使用resize:")
        for key, value in stats1.items():
            print(f"  {key}: {value}")
        
        print("使用resize:")
        for key, value in stats2.items():
            print(f"  {key}: {value}")
        
        # 比较文件大小
        if os.path.exists("outputs/compare_no_resize.mp4"):
            size1 = os.path.getsize("outputs/compare_no_resize.mp4") / (1024 * 1024)
            print(f"\n不使用resize文件大小: {size1:.2f} MB")
        
        if os.path.exists("outputs/compare_with_resize.mp4"):
            size2 = os.path.getsize("outputs/compare_with_resize.mp4") / (1024 * 1024)
            print(f"使用resize文件大小: {size2:.2f} MB")
            if 'size1' in locals():
                print(f"文件大小减少: {((size1 - size2) / size1 * 100):.1f}%")
        
        # 清理临时文件
        if os.path.exists(temp_video):
            os.remove(temp_video)
        
    except Exception as e:
        print(f"比较测试失败: {e}")


def main():
    """
    /*
     * @Name: main
     * @Description: 主函数
     *
     * @Input
     * None
     *
     * @Output
     * None
     *
     * @Edit History
     * Date: 2024-12-19
     * Time: 16:30:00
     * Author: JiaTao
     * Content: 主函数
     */
    """
    print("Resize功能测试脚本")
    print("1. 基本resize测试")
    print("2. 比较resize效果")
    print("0. 退出")
    
    choice = input("\n请选择测试项目 (0-2): ").strip()
    
    # 创建输出目录
    os.makedirs("outputs", exist_ok=True)
    
    if choice == "1":
        test_resize_functionality()
    elif choice == "2":
        compare_with_without_resize()
    elif choice == "0":
        print("退出测试")
    else:
        print("无效选择")


if __name__ == "__main__":
    main()
