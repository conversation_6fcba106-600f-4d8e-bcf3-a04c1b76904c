#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
目标跟踪系统演示脚本
展示系统的主要功能和使用方法
"""

import os
import sys
import time
import argparse
from pathlib import Path

from object_tracker import ObjectTracker
from tracker_config import Config<PERSON>anager, create_default_configs
from video_processor import VideoProcessor


def demo_config_management():
    """
    /*
     * @Name: demo_config_management
     * @Description: 演示配置管理功能
     *
     * @Input
     * None
     *
     * @Output
     * None
     *
     * @Edit History
     * Date: 2024-12-19
     * Time: 14:30:00
     * Author: JiaTao
     * Content: 演示配置管理功能
     */
    """
    print("\n=== 配置管理演示 ===")
    
    # 创建默认配置
    print("1. 创建默认配置文件...")
    create_default_configs()
    
    # 加载和修改配置
    print("2. 加载和修改配置...")
    config_manager = ConfigManager()
    
    print("原始配置:")
    config_manager.print_config()
    
    # 更新配置
    config_manager.update_config(
        track_high_thresh=0.3,
        track_low_thresh=0.15,
        conf_threshold=0.3,
        iou_threshold=0.5
    )
    
    print("\n更新后配置:")
    config_manager.print_config()
    
    # 保存自定义配置
    custom_config_path = "demo_config.yaml"
    config_manager.save_config(custom_config_path)
    print(f"\n自定义配置已保存到: {custom_config_path}")


def demo_video_info():
    """
    /*
     * @Name: demo_video_info
     * @Description: 演示视频信息获取功能
     *
     * @Input
     * None
     *
     * @Output
     * None
     *
     * @Edit History
     * Date: 2024-12-19
     * Time: 14:30:00
     * Author: JiaTao
     * Content: 演示视频信息获取功能
     */
    """
    print("\n=== 视频信息演示 ===")
    
    # 查找示例视频文件
    video_extensions = ['.mp4', '.avi', '.mov', '.mkv']
    sample_videos = []
    
    for ext in video_extensions:
        for video_file in Path('.').glob(f'*{ext}'):
            sample_videos.append(str(video_file))
            break  # 只取第一个找到的文件
    
    if sample_videos:
        video_path = sample_videos[0]
        print(f"分析视频文件: {video_path}")
        try:
            VideoProcessor.print_video_info(video_path)
        except Exception as e:
            print(f"获取视频信息失败: {e}")
    else:
        print("未找到示例视频文件")
        print("支持的格式: MP4, AVI, MOV, MKV")


def demo_tracker_initialization():
    """
    /*
     * @Name: demo_tracker_initialization
     * @Description: 演示跟踪器初始化
     *
     * @Input
     * None
     *
     * @Output
     * None
     *
     * @Edit History
     * Date: 2024-12-19
     * Time: 14:30:00
     * Author: JiaTao
     * Content: 演示跟踪器初始化过程
     */
    """
    print("\n=== 跟踪器初始化演示 ===")
    
    # 检查模型文件
    model_path = "yolo11m.pt"
    if not os.path.exists(model_path):
        print(f"模型文件 {model_path} 不存在")
        print("首次运行时，Ultralytics会自动下载模型文件")
        print("这可能需要一些时间，请耐心等待...")
    
    try:
        print("正在初始化跟踪器...")
        start_time = time.time()
        
        tracker = ObjectTracker(
            model_path=model_path,
            tracker_config="botsort.yaml",
            conf_threshold=0.25,
            iou_threshold=0.45
        )
        
        init_time = time.time() - start_time
        print(f"✓ 跟踪器初始化成功 (耗时: {init_time:.2f}秒)")
        
        # 显示跟踪器信息
        print(f"模型路径: {tracker.model_path}")
        print(f"跟踪器配置: {tracker.tracker_config}")
        print(f"置信度阈值: {tracker.conf_threshold}")
        print(f"IoU阈值: {tracker.iou_threshold}")
        
        return tracker
        
    except Exception as e:
        print(f"✗ 跟踪器初始化失败: {e}")
        return None


def demo_tracking_features(tracker):
    """
    /*
     * @Name: demo_tracking_features
     * @Description: 演示跟踪功能
     *
     * @Input
     * tracker: ObjectTracker - 跟踪器实例
     *
     * @Output
     * None
     *
     * @Edit History
     * Date: 2024-12-19
     * Time: 14:30:00
     * Author: JiaTao
     * Content: 演示跟踪功能
     */
    """
    if tracker is None:
        print("跟踪器未初始化，跳过跟踪演示")
        return
    
    print("\n=== 跟踪功能演示 ===")
    
    # 查找视频文件
    video_files = []
    for ext in ['.mp4', '.avi', '.mov', '.mkv']:
        video_files.extend(Path('.').glob(f'*{ext}'))
    
    if video_files:
        video_path = str(video_files[0])
        print(f"使用视频文件: {video_path}")
        
        try:
            print("开始视频跟踪演示...")
            print("注意: 这只是一个快速演示，按 'q' 键可以提前退出")
            
            stats = tracker.track_video(
                video_path=video_path,
                output_path="demo_output.mp4",
                show_display=True,
                save_frames=False
            )
            
            print("跟踪完成！统计信息:")
            for key, value in stats.items():
                print(f"  {key}: {value}")
            
            if os.path.exists("demo_output.mp4"):
                print("输出视频已保存为: demo_output.mp4")
        
        except Exception as e:
            print(f"跟踪演示失败: {e}")
    
    else:
        print("未找到视频文件，演示摄像头跟踪...")
        try:
            print("开始摄像头跟踪演示...")
            print("按 'q' 键退出摄像头跟踪")
            
            tracker.track_realtime(
                camera_index=0,
                output_path="demo_camera.mp4"
            )
            
        except Exception as e:
            print(f"摄像头跟踪演示失败: {e}")
            print("可能的原因:")
            print("- 没有可用的摄像头")
            print("- 摄像头被其他程序占用")
            print("- 权限不足")


def interactive_demo():
    """
    /*
     * @Name: interactive_demo
     * @Description: 交互式演示
     *
     * @Input
     * None
     *
     * @Output
     * None
     *
     * @Edit History
     * Date: 2024-12-19
     * Time: 14:30:00
     * Author: JiaTao
     * Content: 提供交互式演示界面
     */
    """
    print("=== YOLOv11 + BotSORT 目标跟踪系统演示 ===")
    print("本演示将展示系统的主要功能")
    
    while True:
        print("\n请选择演示内容:")
        print("1. 配置管理演示")
        print("2. 视频信息获取演示")
        print("3. 跟踪器初始化演示")
        print("4. 跟踪功能演示")
        print("5. 完整演示")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-5): ").strip()
        
        if choice == '0':
            print("演示结束，感谢使用！")
            break
        elif choice == '1':
            demo_config_management()
        elif choice == '2':
            demo_video_info()
        elif choice == '3':
            tracker = demo_tracker_initialization()
        elif choice == '4':
            tracker = demo_tracker_initialization()
            demo_tracking_features(tracker)
        elif choice == '5':
            # 完整演示
            demo_config_management()
            demo_video_info()
            tracker = demo_tracker_initialization()
            demo_tracking_features(tracker)
        else:
            print("无效选择，请重新输入")


def main():
    """
    /*
     * @Name: main
     * @Description: 主函数
     *
     * @Input
     * None
     *
     * @Output
     * None
     *
     * @Edit History
     * Date: 2024-12-19
     * Time: 14:30:00
     * Author: JiaTao
     * Content: 演示脚本主函数
     */
    """
    parser = argparse.ArgumentParser(description="目标跟踪系统演示")
    parser.add_argument("--auto", action="store_true", help="自动运行完整演示")
    parser.add_argument("--config", action="store_true", help="仅演示配置管理")
    parser.add_argument("--video-info", action="store_true", help="仅演示视频信息")
    parser.add_argument("--tracker", action="store_true", help="仅演示跟踪器")
    
    args = parser.parse_args()
    
    if args.auto:
        # 自动完整演示
        print("=== 自动完整演示模式 ===")
        demo_config_management()
        demo_video_info()
        tracker = demo_tracker_initialization()
        demo_tracking_features(tracker)
    elif args.config:
        demo_config_management()
    elif args.video_info:
        demo_video_info()
    elif args.tracker:
        tracker = demo_tracker_initialization()
        demo_tracking_features(tracker)
    else:
        # 交互式演示
        interactive_demo()


if __name__ == "__main__":
    main()
