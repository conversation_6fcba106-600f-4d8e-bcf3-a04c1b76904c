#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
目标跟踪系统主程序
提供命令行接口和图形界面入口
"""

import argparse
import os
import sys
from typing import Optional
import logging

from object_tracker import ObjectTracker
from tracker_config import ConfigManager, create_default_configs
from video_processor import VideoProcessor


def setup_logging(log_level: str = "INFO") -> None:
    """
    /*
     * @Name: setup_logging
     * @Description: 设置全局日志配置
     *
     * @Input
     * log_level: str - 日志级别
     *
     * @Output
     * None
     *
     * @Edit History
     * Date: 2024-12-19
     * Time: 14:30:00
     * Author: JiaTao
     * Content: 配置全局日志系统
     */
    """
    level = getattr(logging, log_level.upper(), logging.INFO)
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('tracker_main.log', encoding='utf-8')
        ]
    )


def parse_arguments() -> argparse.Namespace:
    """
    /*
     * @Name: parse_arguments
     * @Description: 解析命令行参数
     *
     * @Input
     * None
     *
     * @Output
     * argparse.Namespace - 解析后的参数
     *
     * @Edit History
     * Date: 2024-12-19
     * Time: 14:30:00
     * Author: JiaTao
     * Content: 定义和解析命令行参数
     */
    """
    parser = argparse.ArgumentParser(
        description="YOLOv11 + BotSORT 目标跟踪系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 跟踪视频文件
  python main.py --source video.mp4 --output tracked_video.mp4

  # 跟踪视频并resize到960分辨率
  python main.py --source video.mp4 --output tracked_video.mp4 --resize

  # 实时摄像头跟踪
  python main.py --source 0 --realtime

  # 使用自定义配置
  python main.py --source video.mp4 --config custom_config.yaml

  # 显示视频信息
  python main.py --info video.mp4
        """
    )
    
    # 基本参数
    parser.add_argument(
        '--source', '-s',
        type=str,
        help='输入源：视频文件路径或摄像头索引(0,1,2...)'
    )
    
    parser.add_argument(
        '--output', '-o',
        type=str,
        help='输出视频文件路径'
    )
    
    parser.add_argument(
        '--model', '-m',
        type=str,
        default='yolo11m.pt',
        help='YOLO模型权重文件路径 (默认: yolo11m.pt)'
    )
    
    parser.add_argument(
        '--tracker',
        type=str,
        default='botsort.yaml',
        help='跟踪器配置文件 (默认: botsort.yaml)'
    )
    
    # 跟踪参数
    parser.add_argument(
        '--conf',
        type=float,
        default=0.25,
        help='置信度阈值 (默认: 0.25)'
    )
    
    parser.add_argument(
        '--iou',
        type=float,
        default=0.45,
        help='IoU阈值 (默认: 0.45)'
    )
    
    parser.add_argument(
        '--device',
        type=str,
        default='auto',
        help='推理设备 (默认: auto)'
    )
    
    # 显示和保存选项
    parser.add_argument(
        '--show',
        action='store_true',
        help='显示实时跟踪结果'
    )
    
    parser.add_argument(
        '--save-frames',
        action='store_true',
        help='保存跟踪帧图像'
    )

    parser.add_argument(
        '--realtime',
        action='store_true',
        help='实时摄像头跟踪模式'
    )

    parser.add_argument(
        '--resize',
        action='store_true',
        help='将输出视频resize到960分辨率，保持长宽比不变'
    )
    
    # 配置管理
    parser.add_argument(
        '--config',
        type=str,
        help='自定义配置文件路径'
    )
    
    parser.add_argument(
        '--create-config',
        action='store_true',
        help='创建默认配置文件'
    )
    
    # 工具功能
    parser.add_argument(
        '--info',
        type=str,
        help='显示视频文件信息'
    )
    
    parser.add_argument(
        '--log-level',
        type=str,
        default='INFO',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        help='日志级别 (默认: INFO)'
    )
    
    return parser.parse_args()


def validate_arguments(args: argparse.Namespace) -> bool:
    """
    /*
     * @Name: validate_arguments
     * @Description: 验证命令行参数
     *
     * @Input
     * args: argparse.Namespace - 命令行参数
     *
     * @Output
     * bool - 参数是否有效
     *
     * @Edit History
     * Date: 2024-12-19
     * Time: 14:30:00
     * Author: JiaTao
     * Content: 验证命令行参数的有效性
     */
    """
    # 检查模型文件
    if not os.path.exists(args.model):
        print(f"错误: 模型文件不存在: {args.model}")
        return False
    
    # 检查输入源
    if args.source:
        # 如果是数字，认为是摄像头索引
        if args.source.isdigit():
            return True
        # 否则检查视频文件
        elif not os.path.exists(args.source):
            print(f"错误: 输入文件不存在: {args.source}")
            return False
        elif not VideoProcessor.validate_video_path(args.source):
            print(f"错误: 不支持的视频格式: {args.source}")
            return False
    
    # 检查配置文件
    if args.config and not os.path.exists(args.config):
        print(f"错误: 配置文件不存在: {args.config}")
        return False
    
    return True


def main():
    """
    /*
     * @Name: main
     * @Description: 主程序入口函数
     *
     * @Input
     * None
     *
     * @Output
     * None
     *
     * @Edit History
     * Date: 2024-12-19
     * Time: 14:30:00
     * Author: JiaTao
     * Content: 主程序逻辑实现
     */
    """
    # 解析命令行参数
    args = parse_arguments()
    
    # 设置日志
    setup_logging(args.log_level)
    logger = logging.getLogger(__name__)
    
    try:
        # 创建默认配置文件
        if args.create_config:
            create_default_configs()
            print("默认配置文件已创建")
            return
        
        # 显示视频信息
        if args.info:
            VideoProcessor.print_video_info(args.info)
            return
        
        # 验证参数
        if not args.source:
            print("错误: 请指定输入源 (--source)")
            return
        
        if not validate_arguments(args):
            return
        
        # 加载配置
        config_manager = ConfigManager(args.config)
        if args.config:
            logger.info(f"使用自定义配置: {args.config}")
        
        # 更新配置参数
        config_manager.update_config(
            conf_threshold=args.conf,
            iou_threshold=args.iou,
            device=args.device
        )
        
        # 创建跟踪器
        tracker = ObjectTracker(
            model_path=args.model,
            tracker_config=args.tracker,
            conf_threshold=args.conf,
            iou_threshold=args.iou,
            device=args.device
        )
        
        logger.info("目标跟踪器初始化完成")
        
        # 执行跟踪
        if args.realtime or args.source.isdigit():
            # 实时摄像头跟踪
            camera_index = int(args.source) if args.source.isdigit() else 0
            tracker.track_realtime(
                camera_index=camera_index,
                output_path=args.output,
                resize_output=args.resize
            )
        else:
            # 视频文件跟踪
            stats = tracker.track_video(
                video_path=args.source,
                output_path=args.output,
                show_display=args.show,
                save_frames=args.save_frames,
                resize_output=args.resize
            )
            
            # 显示统计信息
            logger.info("跟踪完成，统计信息:")
            for key, value in stats.items():
                logger.info(f"  {key}: {value}")
    
    except KeyboardInterrupt:
        logger.info("用户中断程序")
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
