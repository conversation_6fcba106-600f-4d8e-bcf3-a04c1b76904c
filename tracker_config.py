#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
跟踪器配置管理模块
提供跟踪器参数配置和管理功能
"""

import yaml
import os
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict


@dataclass
class TrackerConfig:
    """
    /*
     * @Name: TrackerConfig
     * @Description: 跟踪器配置数据类
     *
     * @Input
     * 各种跟踪器配置参数
     *
     * @Output
     * 配置数据类实例
     *
     * @Edit History
     * Date: 2024-12-19
     * Time: 14:30:00
     * Author: JiaTao
     * Content: 定义跟踪器配置参数数据类
     */
    """
    # 基础配置
    tracker_type: str = "botsort"
    track_high_thresh: float = 0.25
    track_low_thresh: float = 0.1
    new_track_thresh: float = 0.25
    track_buffer: int = 30
    match_thresh: float = 0.8
    fuse_score: bool = True
    
    # BotSORT特定配置
    gmc_method: str = "sparseOptFlow"
    proximity_thresh: float = 0.5
    appearance_thresh: float = 0.8
    with_reid: bool = False
    model: str = "auto"
    
    # 检测配置
    conf_threshold: float = 0.25
    iou_threshold: float = 0.45
    max_det: int = 300
    
    # 视频处理配置
    imgsz: int = 640
    device: str = "auto"
    half: bool = False
    
    # 输出配置
    save_txt: bool = False
    save_conf: bool = False
    save_crop: bool = False
    show_labels: bool = True
    show_conf: bool = True
    show_boxes: bool = True
    line_width: int = 2


class ConfigManager:
    """
    /*
     * @Name: ConfigManager
     * @Description: 配置管理器类，负责加载、保存和管理跟踪器配置
     *
     * @Input
     * config_path: Optional[str] - 配置文件路径
     *
     * @Output
     * 配置管理器实例
     *
     * @Edit History
     * Date: 2024-12-19
     * Time: 14:30:00
     * Author: JiaTao
     * Content: 创建配置管理器类
     */
     """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        /*
         * @Name: __init__
         * @Description: 初始化配置管理器
         *
         * @Input
         * config_path: Optional[str] - 配置文件路径
         *
         * @Output
         * None
         *
         * @Edit History
         * Date: 2024-12-19
         * Time: 14:30:00
         * Author: JiaTao
         * Content: 初始化配置管理器
         */
        """
        self.config_path = config_path
        self.config = TrackerConfig()
        
        if config_path and os.path.exists(config_path):
            self.load_config(config_path)
    
    def load_config(self, config_path: str) -> None:
        """
        /*
         * @Name: load_config
         * @Description: 从YAML文件加载配置
         *
         * @Input
         * config_path: str - 配置文件路径
         *
         * @Output
         * None
         *
         * @Edit History
         * Date: 2024-12-19
         * Time: 14:30:00
         * Author: JiaTao
         * Content: 从YAML文件加载配置参数
         */
        """
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config_dict = yaml.safe_load(f)
            
            # 更新配置对象
            for key, value in config_dict.items():
                if hasattr(self.config, key):
                    setattr(self.config, key, value)
            
            print(f"成功加载配置文件: {config_path}")
        
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            print("使用默认配置")
    
    def save_config(self, config_path: str) -> None:
        """
        /*
         * @Name: save_config
         * @Description: 保存配置到YAML文件
         *
         * @Input
         * config_path: str - 配置文件保存路径
         *
         * @Output
         * None
         *
         * @Edit History
         * Date: 2024-12-19
         * Time: 14:30:00
         * Author: JiaTao
         * Content: 将当前配置保存到YAML文件
         */
        """
        try:
            config_dict = asdict(self.config)
            
            with open(config_path, 'w', encoding='utf-8') as f:
                yaml.dump(config_dict, f, default_flow_style=False, allow_unicode=True)
            
            print(f"配置已保存到: {config_path}")
        
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def update_config(self, **kwargs) -> None:
        """
        /*
         * @Name: update_config
         * @Description: 更新配置参数
         *
         * @Input
         * **kwargs - 要更新的配置参数
         *
         * @Output
         * None
         *
         * @Edit History
         * Date: 2024-12-19
         * Time: 14:30:00
         * Author: JiaTao
         * Content: 动态更新配置参数
         */
        """
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
                print(f"更新配置: {key} = {value}")
            else:
                print(f"警告: 未知配置参数 {key}")
    
    def get_config_dict(self) -> Dict[str, Any]:
        """
        /*
         * @Name: get_config_dict
         * @Description: 获取配置字典
         *
         * @Input
         * None
         *
         * @Output
         * Dict[str, Any] - 配置参数字典
         *
         * @Edit History
         * Date: 2024-12-19
         * Time: 14:30:00
         * Author: JiaTao
         * Content: 返回配置参数字典
         */
        """
        return asdict(self.config)
    
    def get_tracker_config(self) -> Dict[str, Any]:
        """
        /*
         * @Name: get_tracker_config
         * @Description: 获取跟踪器相关配置
         *
         * @Input
         * None
         *
         * @Output
         * Dict[str, Any] - 跟踪器配置字典
         *
         * @Edit History
         * Date: 2024-12-19
         * Time: 14:30:00
         * Author: JiaTao
         * Content: 返回跟踪器相关配置参数
         */
        """
        tracker_keys = [
            'tracker_type', 'track_high_thresh', 'track_low_thresh',
            'new_track_thresh', 'track_buffer', 'match_thresh', 'fuse_score',
            'gmc_method', 'proximity_thresh', 'appearance_thresh', 'with_reid', 'model'
        ]
        
        config_dict = asdict(self.config)
        return {key: config_dict[key] for key in tracker_keys if key in config_dict}
    
    def create_custom_tracker_config(self, output_path: str) -> str:
        """
        /*
         * @Name: create_custom_tracker_config
         * @Description: 创建自定义跟踪器配置文件
         *
         * @Input
         * output_path: str - 输出配置文件路径
         *
         * @Output
         * str - 创建的配置文件路径
         *
         * @Edit History
         * Date: 2024-12-19
         * Time: 14:30:00
         * Author: JiaTao
         * Content: 创建自定义跟踪器配置文件
         */
        """
        tracker_config = self.get_tracker_config()
        
        # 添加配置文件头部注释
        config_content = f"""# 自定义跟踪器配置文件
# 基于{self.config.tracker_type.upper()}跟踪算法
# 生成时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

"""
        
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(config_content)
                yaml.dump(tracker_config, f, default_flow_style=False, allow_unicode=True)
            
            print(f"自定义跟踪器配置文件已创建: {output_path}")
            return output_path
        
        except Exception as e:
            print(f"创建配置文件失败: {e}")
            return ""
    
    def print_config(self) -> None:
        """
        /*
         * @Name: print_config
         * @Description: 打印当前配置信息
         *
         * @Input
         * None
         *
         * @Output
         * None
         *
         * @Edit History
         * Date: 2024-12-19
         * Time: 14:30:00
         * Author: JiaTao
         * Content: 格式化打印当前配置信息
         */
        """
        print("\n=== 当前跟踪器配置 ===")
        config_dict = asdict(self.config)
        
        for key, value in config_dict.items():
            print(f"{key:20}: {value}")
        print("=" * 30)


def create_default_configs():
    """
    /*
     * @Name: create_default_configs
     * @Description: 创建默认配置文件
     *
     * @Input
     * None
     *
     * @Output
     * None
     *
     * @Edit History
     * Date: 2024-12-19
     * Time: 14:30:00
     * Author: JiaTao
     * Content: 创建默认的BotSORT和ByteTrack配置文件
     */
    """
    # 创建configs目录
    os.makedirs("configs", exist_ok=True)
    
    # BotSORT配置
    botsort_config = ConfigManager()
    botsort_config.update_config(
        tracker_type="botsort",
        track_high_thresh=0.25,
        track_low_thresh=0.1,
        new_track_thresh=0.25,
        track_buffer=30,
        match_thresh=0.8,
        fuse_score=True,
        gmc_method="sparseOptFlow",
        proximity_thresh=0.5,
        appearance_thresh=0.8,
        with_reid=False
    )
    botsort_config.create_custom_tracker_config("configs/botsort_custom.yaml")
    
    # ByteTrack配置
    bytetrack_config = ConfigManager()
    bytetrack_config.update_config(
        tracker_type="bytetrack",
        track_high_thresh=0.25,
        track_low_thresh=0.1,
        new_track_thresh=0.25,
        track_buffer=30,
        match_thresh=0.8,
        fuse_score=True
    )
    bytetrack_config.create_custom_tracker_config("configs/bytetrack_custom.yaml")
    
    print("默认配置文件已创建完成")


if __name__ == "__main__":
    # 创建默认配置文件
    create_default_configs()
    
    # 示例用法
    config_manager = ConfigManager()
    config_manager.print_config()
