<!-- Ultralytics 🚀 AGPL-3.0 License - https://ultralytics.com/license -->

<!--Similarity search webpage-->
<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Semantic Image Search</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap"
      rel="stylesheet"
    />
    <style>
      body {
        background: linear-gradient(135deg, #f0f4ff, #f9fbff);
        font-family: "Inter", sans-serif;
        color: #111e68;
        padding: 2rem;
        margin: 0;
        min-height: 100vh;
      }

      h1 {
        text-align: center;
        margin-bottom: 2rem;
        font-size: 2.5rem;
        font-weight: 600;
      }

      form {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        align-items: center;
        gap: 1rem;
        margin-bottom: 3rem;
        animation: fadeIn 1s ease-in-out;
      }

      input[type="text"] {
        width: 300px;
        padding: 0.75rem 1rem;
        font-size: 1rem;
        border-radius: 10px;
        border: 1px solid #ccc;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
        transition: box-shadow 0.3s ease;
      }

      input[type="text"]:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(17, 30, 104, 0.2);
      }

      button {
        background-color: #111e68;
        color: white;
        font-weight: 600;
        font-size: 1rem;
        padding: 0.75rem 1.5rem;
        border-radius: 10px;
        border: none;
        cursor: pointer;
        transition:
          background-color 0.3s ease,
          transform 0.2s ease;
      }

      button:hover {
        background-color: #1f2e9f;
        transform: translateY(-2px);
      }

      .grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
        gap: 1.5rem;
        max-width: 1600px;
        margin: auto;
        animation: fadeInUp 1s ease-in-out;
      }

      .card {
        background: white;
        border-radius: 16px;
        overflow: hidden;
        box-shadow: 0 6px 14px rgba(0, 0, 0, 0.08);
        transition:
          transform 0.3s ease,
          box-shadow 0.3s ease;
      }

      .card:hover {
        transform: translateY(-6px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
      }

      .card img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        display: block;
      }

      @keyframes fadeIn {
        0% {
          opacity: 0;
          transform: scale(0.95);
        }
        100% {
          opacity: 1;
          transform: scale(1);
        }
      }

      @keyframes fadeInUp {
        0% {
          opacity: 0;
          transform: translateY(20px);
        }
        100% {
          opacity: 1;
          transform: translateY(0);
        }
      }
    </style>
  </head>
  <body>
    <div style="text-align: center; margin-bottom: 1rem">
      <img
        src="https://raw.githubusercontent.com/ultralytics/assets/main/logo/favicon.png"
        alt="Ultralytics Logo"
        style="height: 40px"
      />
    </div>
    <h1>Semantic Image Search with AI</h1>

    <!-- Search box -->
    <form method="POST">
      <input
        type="text"
        name="query"
        placeholder="Describe the scene (e.g., man walking)"
        value="{{ request.form['query'] }}"
        required
      />
      <button type="submit">Search</button>
    </form>

    <!-- Search results grid -->
    <div class="grid">
      {% for img in results %}
      <div class="card">
        <img src="{{ url_for('static', filename=img) }}" alt="Result Image" />
      </div>
      {% endfor %}
    </div>
  </body>
</html>
